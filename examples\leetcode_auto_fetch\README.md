# LeetCode Auto-Fetch Examples

This directory contains examples demonstrating Pyleet's new auto-fetch feature that automatically retrieves test cases from LeetCode problems.

## Features

- **Automatic Test Case Retrieval**: Fetch example test cases directly from LeetCode problems
- **Caching**: Downloaded test cases are cached locally to avoid repeated API calls
- **CLI Integration**: Use `--leetcode` flag instead of `--testcases`
- **Programmatic Interface**: Use `pyleet.run_leetcode()` function

## Files

### `two_sum_example.py`
Demonstrates auto-fetching test cases for the classic "Two Sum" problem:
- Shows basic usage of `pyleet.run_leetcode()`
- Includes error handling for network issues
- Provides fallback to manual test cases

**Run it:**
```bash
python two_sum_example.py
```

## CLI Usage

### Auto-fetch from LeetCode
```bash
# Auto-fetch test cases for "two-sum" problem
pyleet solution.py --leetcode two-sum

# With method selection
pyleet solution.py --leetcode two-sum --method twoSum

# Disable caching
pyleet solution.py --leetcode two-sum --no-cache
```

### Traditional file-based approach (still supported)
```bash
pyleet solution.py --testcases cases.json
```

## Programmatic Usage

### Basic auto-fetch
```python
import pyleet

# Auto-fetch test cases for "two-sum" problem
results = pyleet.run_leetcode("two-sum")
pyleet.print_results(results)
```

### With method selection
```python
results = pyleet.run_leetcode("two-sum", method="twoSum")
```

### Disable caching
```python
results = pyleet.run_leetcode("two-sum", cache_enabled=False)
```

### Error handling
```python
try:
    results = pyleet.run_leetcode("two-sum")
    pyleet.print_results(results)
except pyleet.LeetCodeAPIError as e:
    print(f"Failed to fetch from LeetCode: {e}")
    # Fallback to manual test cases
```

## Supported Problem Formats

The auto-fetch feature works with most LeetCode problems that have example test cases. Common problem types include:

- **Array problems**: two-sum, three-sum, etc.
- **String problems**: valid-parentheses, longest-substring, etc.
- **Tree problems**: invert-binary-tree, maximum-depth, etc.
- **Linked list problems**: reverse-linked-list, merge-two-lists, etc.

## Problem Slug Format

LeetCode problem slugs are the URL-friendly names used in problem URLs:
- URL: `https://leetcode.com/problems/two-sum/`
- Slug: `two-sum`

## Caching

- Test cases are cached in `.pyleet_cache/` directory
- Cache files are named using MD5 hash of the problem slug
- Use `--no-cache` flag or `cache_enabled=False` to disable caching
- Cached files are JSON format and can be manually inspected

## Error Handling

The auto-fetch feature includes robust error handling:
- Network connectivity issues
- Invalid problem slugs
- API rate limiting
- Parsing errors for unusual test case formats

## Benefits

1. **No Manual Test Case Creation**: Skip the tedious process of manually creating test case files
2. **Always Up-to-Date**: Get the latest example test cases directly from LeetCode
3. **Consistent Format**: Test cases are automatically formatted for Pyleet
4. **Offline Support**: Cached test cases work without internet connection
5. **Fallback Support**: Easy to fall back to manual test cases if needed

## Limitations

- Only fetches example test cases (not hidden test cases)
- Requires internet connection for first fetch
- May not work with very new or unusual problem formats
- Subject to LeetCode's API availability and rate limits

## Getting Started

1. Choose a LeetCode problem (e.g., "two-sum")
2. Write your solution as usual
3. Use auto-fetch instead of creating test case files:
   - CLI: `pyleet solution.py --leetcode two-sum`
   - Programmatic: `pyleet.run_leetcode("two-sum")`
4. Enjoy automatic test case retrieval!
