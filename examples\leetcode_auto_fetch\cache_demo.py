"""
Demonstration of Pyleet's caching functionality for LeetCode auto-fetch.

This script shows how to manually create cache files and use them
when the LeetCode API is not accessible.
"""

import os
import json
import hashlib
import pyleet


class Solution:
    def twoSum(self, nums, target):
        """Two Sum solution for demonstration."""
        num_map = {}
        for i, num in enumerate(nums):
            complement = target - num
            if complement in num_map:
                return [num_map[complement], i]
            num_map[num] = i
        return []


def create_sample_cache():
    """Create a sample cache file for the two-sum problem."""

    # Create cache directory
    cache_dir = ".pyleet_cache"
    os.makedirs(cache_dir, exist_ok=True)

    # Calculate cache filename (same way as LeetCodeAPI)
    problem_slug = "two-sum"
    cache_key = hashlib.md5(problem_slug.encode()).hexdigest()
    cache_path = os.path.join(cache_dir, f"{cache_key}.json")

    # Sample problem data (as would be returned by LeetCode API)
    sample_data = {
        "questionId": "1",
        "title": "Two Sum",
        "titleSlug": "two-sum",
        "difficulty": "Easy",
        "exampleTestcases": "[2,7,11,15]\n9\n[0,1]\n\n[3,2,4]\n6\n[1,2]\n\n[3,3]\n6\n[0,1]",
        "metaData": json.dumps({
            "name": "twoSum",
            "params": [
                {"name": "nums", "type": "integer[]"},
                {"name": "target", "type": "integer"}
            ],
            "return": {"type": "integer[]"}
        })
    }

    # Save to cache
    with open(cache_path, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, indent=2)

    print(f"✅ Created sample cache file: {cache_path}")
    return cache_path


def demonstrate_cache_usage():
    """Demonstrate using cached data when API is not available."""

    print("=== Pyleet Cache Demonstration ===")
    print()

    # Create sample cache
    cache_path = create_sample_cache()

    try:
        print("Attempting to use cached data for 'two-sum' problem...")

        # This will use the cached data we just created
        results = pyleet.run_leetcode("two-sum", method="twoSum")

        print(f"✅ Successfully loaded {len(results)} test cases from cache!")
        print()

        # Show results
        pyleet.print_results(results, verbose=False)

        print("\n=== Cache Information ===")
        print(f"Cache file: {cache_path}")
        print(f"Cache size: {os.path.getsize(cache_path)} bytes")

        # Show cache contents
        print("\nCache contents preview:")
        with open(cache_path, 'r') as f:
            cache_data = json.load(f)
            print(f"  Problem: {cache_data.get('title', 'Unknown')}")
            print(f"  Difficulty: {cache_data.get('difficulty', 'Unknown')}")
            test_cases_text = cache_data.get('exampleTestcases', '')
            test_case_count = len(test_cases_text.split(
                '\n\n')) if test_cases_text else 0
            print(f"  Test cases: {test_case_count}")

    except Exception as e:
        print(f"❌ Error: {e}")

    finally:
        # Clean up
        if os.path.exists(cache_path):
            os.remove(cache_path)
            print(f"\n🧹 Cleaned up cache file: {cache_path}")


if __name__ == "__main__":
    demonstrate_cache_usage()
