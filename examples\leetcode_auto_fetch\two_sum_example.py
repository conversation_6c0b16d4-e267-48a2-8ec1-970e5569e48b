"""
Example demonstrating auto-fetching test cases from LeetCode.

This example shows how to use Pyleet's new auto-fetch feature to automatically
retrieve test cases from LeetCode problems without manually creating test case files.
"""

import pyleet


class Solution:
    def twoSum(self, nums, target):
        """
        LeetCode Problem 1: Two Sum
        Find two numbers that add up to target.
        """
        print(f"Processing nums={nums}, target={target}")
        num_map = {}
        for i, num in enumerate(nums):
            complement = target - num
            if complement in num_map:
                return [num_map[complement], i]
            num_map[num] = i
        return []


if __name__ == "__main__":
    print("=== Auto-fetching test cases from LeetCode ===")
    print("Note: LeetCode may block automated requests, so this example includes fallback logic.")
    print()

    try:
        # Auto-fetch test cases for the "two-sum" problem from LeetCode
        print("Attempting to fetch test cases from LeetCode...")
        results = pyleet.run_leetcode("two-sum", method="twoSum")

        print(f"✅ Successfully fetched and ran {len(results)} test cases!")
        pyleet.print_results(results)

    except pyleet.LeetCodeAPIError as e:
        print(f"❌ Failed to fetch test cases from LeetCode:")
        print(f"   {e}")
        print()
        print("This is expected behavior as LeetCode has anti-bot measures.")
        print("The auto-fetch feature works when LeetCode allows the requests.")

        # Fallback to manual test cases
        print("\n=== Falling back to manual test cases ===")
        manual_testcases = [
            (([2, 7, 11, 15], 9), [0, 1]),
            (([3, 2, 4], 6), [1, 2]),
            (([3, 3], 6), [0, 1])
        ]

        print("Running with manually defined test cases...")
        results = pyleet.run(manual_testcases, method="twoSum")
        pyleet.print_results(results)

        print("\n=== How to use the auto-fetch feature ===")
        print("1. CLI: pyleet solution.py --leetcode two-sum")
        print("2. Programmatic: pyleet.run_leetcode('two-sum')")
        print("3. The feature includes caching, so successful fetches are stored locally")
        print("4. Use --no-cache or cache_enabled=False to disable caching")

    except Exception as e:
        print(f"An unexpected error occurred: {e}")
