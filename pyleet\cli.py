"""
CLI entry point for Pyleet.
"""

import argparse
import sys
import importlib.util
from pyleet.testcase_loader import load_test_cases, process_test_cases
from pyleet.runner import run_solution
from pyleet.datastructures import set_user_module
from pyleet.colors import green, red
from pyleet.leetcode_api import fetch_leetcode_testcases, LeetCodeAPIError


def main():
    parser = argparse.ArgumentParser(
        description="Run LeetCode Python solutions locally with test cases.")
    parser.add_argument("solution", help="Path to the solution .py file")

    # Make testcases and leetcode mutually exclusive
    test_group = parser.add_mutually_exclusive_group(required=True)
    test_group.add_argument("--testcases", "-t",
                            help="Path to the test case file (.json or .txt)")
    test_group.add_argument("--leetcode", "-l",
                            help="LeetCode problem slug (e.g., 'two-sum') to auto-fetch test cases")

    parser.add_argument("--method", "-m",
                        help="Specify which method to use for testing (optional)")
    parser.add_argument("--no-cache", action="store_true",
                        help="Disable caching of LeetCode API responses")
    args = parser.parse_args()

    # Load the user's solution module first so deserializers can access user-defined classes
    try:
        module_name = "user_solution"
        spec = importlib.util.spec_from_file_location(
            module_name, args.solution)
        user_module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = user_module
        spec.loader.exec_module(user_module)

        # Set the user module for deserializers to access user-defined classes
        set_user_module(user_module)
    except Exception as e:
        print(f"Error loading solution file: {e}")
        sys.exit(1)

    # Load test cases from file or LeetCode API
    try:
        if args.testcases:
            # Load from file
            test_cases = load_test_cases(args.testcases)
        else:
            # Fetch from LeetCode API
            print(
                f"Fetching test cases for problem '{args.leetcode}' from LeetCode...")
            cache_enabled = not args.no_cache
            raw_test_cases = fetch_leetcode_testcases(
                args.leetcode, cache_enabled=cache_enabled)
            # Process the raw test cases to apply deserialization
            test_cases = process_test_cases(raw_test_cases)
            print(f"Successfully fetched {len(test_cases)} test case(s)")
    except LeetCodeAPIError as e:
        print(f"Error fetching test cases from LeetCode: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error loading test cases: {e}")
        sys.exit(1)

    try:
        results = run_solution(args.solution, test_cases,
                               target_method=args.method)
    except Exception as e:
        print(f"Error running solution: {e}")
        sys.exit(1)

    passed_count = 0
    for idx, result in enumerate(results, 1):
        if result["passed"]:
            status = green("PASS", bold=True)
            passed_count += 1
        else:
            status = red("FAIL", bold=True)

        print(f"Test Case {idx}: {status}")
        print(f"  Input: {result['input']}")
        print(f"  Expected: {result['expected']}")

        # Color the actual output based on pass/fail status
        if result["passed"]:
            print(f"  Actual: {result['actual']}")
        else:
            print(f"  Actual: {red(str(result['actual']))}")

        # Display captured print output if any
        if result.get("print_output") and result["print_output"].strip():
            print(f"  Print Output:")
            # Indent each line of print output for clear association
            for line in result["print_output"].rstrip('\n').split('\n'):
                print(f"    {line}")

        print()

    total = len(results)
    if passed_count == total:
        summary_text = green(
            f"Passed {str(passed_count)}/{total} test cases.")
    else:
        summary_text = red(
            f"Passed {str(passed_count)}/{total} test cases.")
    print(summary_text)


if __name__ == "__main__":
    main()
