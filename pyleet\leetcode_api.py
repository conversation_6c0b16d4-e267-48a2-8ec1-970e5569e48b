"""
LeetCode API client for auto-retrieving test cases from LeetCode problems.
"""

import json
import re
import os
import hashlib
from typing import List, Tuple, Dict, Any, Optional
from urllib.request import urlopen, Request
from urllib.error import URLError, HTTPError


class LeetCodeAPIError(Exception):
    """Exception raised for LeetCode API related errors."""
    pass


class LeetCodeAPI:
    """
    Client for interacting with LeetCode's GraphQL API to fetch problem details
    and example test cases.
    """

    BASE_URL = "https://leetcode.com"
    GRAPHQL_URL = f"{BASE_URL}/graphql"
    CACHE_DIR = ".pyleet_cache"

    def __init__(self, cache_enabled: bool = True):
        """
        Initialize the LeetCode API client.

        Args:
            cache_enabled (bool): Whether to enable caching of API responses.
        """
        self.cache_enabled = cache_enabled
        if cache_enabled:
            os.makedirs(self.CACHE_DIR, exist_ok=True)

    def _get_cache_path(self, problem_slug: str) -> str:
        """Get the cache file path for a problem."""
        cache_key = hashlib.md5(problem_slug.encode()).hexdigest()
        return os.path.join(self.CACHE_DIR, f"{cache_key}.json")

    def _load_from_cache(self, problem_slug: str) -> Optional[Dict[str, Any]]:
        """Load problem data from cache if available."""
        if not self.cache_enabled:
            return None

        cache_path = self._get_cache_path(problem_slug)
        if os.path.exists(cache_path):
            try:
                with open(cache_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                # Remove corrupted cache file
                try:
                    os.remove(cache_path)
                except OSError:
                    pass
        return None

    def _save_to_cache(self, problem_slug: str, data: Dict[str, Any]) -> None:
        """Save problem data to cache."""
        if not self.cache_enabled:
            return

        cache_path = self._get_cache_path(problem_slug)
        try:
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
        except IOError:
            # Ignore cache write errors
            pass

    def _get_csrf_token(self) -> str:
        """
        Get CSRF token from LeetCode homepage.

        Returns:
            str: CSRF token

        Raises:
            LeetCodeAPIError: If unable to get CSRF token
        """
        try:
            request = Request(self.BASE_URL)
            request.add_header(
                'User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

            with urlopen(request, timeout=10) as response:
                # Look for csrftoken in Set-Cookie headers
                for header_name, header_value in response.headers.items():
                    if header_name.lower() == 'set-cookie' and 'csrftoken=' in header_value:
                        match = re.search(r'csrftoken=([^;]+)', header_value)
                        if match:
                            return match.group(1)

            # Fallback: generate a dummy token (LeetCode sometimes accepts this)
            return "dummy_csrf_token"

        except (URLError, HTTPError) as e:
            # For 403 errors, provide a more helpful message
            if hasattr(e, 'code') and e.code == 403:
                raise LeetCodeAPIError(
                    f"Access denied by LeetCode (HTTP 403). This may be due to:\n"
                    f"  - Rate limiting or anti-bot measures\n"
                    f"  - Network restrictions\n"
                    f"  - Temporary server issues\n"
                    f"Please try again later or use manual test cases as fallback.")
            else:
                raise LeetCodeAPIError(f"Failed to get CSRF token: {e}")

    def _make_graphql_request(self, query: str, variables: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a GraphQL request to LeetCode API.

        Args:
            query (str): GraphQL query string
            variables (Dict[str, Any]): Query variables

        Returns:
            Dict[str, Any]: API response data

        Raises:
            LeetCodeAPIError: If the API request fails
        """
        csrf_token = self._get_csrf_token()

        payload = {
            "query": query,
            "variables": variables
        }

        request = Request(
            self.GRAPHQL_URL,
            data=json.dumps(payload).encode('utf-8'),
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': f'{self.BASE_URL}/problems/',
                'Cookie': f'csrftoken={csrf_token}',
                'X-CSRFToken': csrf_token,
            }
        )

        try:
            with urlopen(request, timeout=15) as response:
                response_data = json.loads(response.read().decode('utf-8'))

                if 'errors' in response_data:
                    error_msg = '; '.join([error.get('message', 'Unknown error')
                                           for error in response_data['errors']])
                    raise LeetCodeAPIError(f"GraphQL errors: {error_msg}")

                return response_data

        except (URLError, HTTPError, json.JSONDecodeError) as e:
            raise LeetCodeAPIError(f"Failed to make GraphQL request: {e}")

    def get_problem_details(self, problem_slug: str) -> Dict[str, Any]:
        """
        Get problem details including example test cases.

        Args:
            problem_slug (str): The problem slug (e.g., "two-sum")

        Returns:
            Dict[str, Any]: Problem details including test cases

        Raises:
            LeetCodeAPIError: If unable to fetch problem details
        """
        # Check cache first
        cached_data = self._load_from_cache(problem_slug)
        if cached_data:
            return cached_data

        query = """
        query questionData($titleSlug: String!) {
            question(titleSlug: $titleSlug) {
                questionId
                title
                titleSlug
                content
                difficulty
                exampleTestcases
                sampleTestCase
                metaData
                codeSnippets {
                    lang
                    langSlug
                    code
                }
            }
        }
        """

        variables = {"titleSlug": problem_slug}

        try:
            response = self._make_graphql_request(query, variables)

            if not response.get('data') or not response['data'].get('question'):
                raise LeetCodeAPIError(f"Problem '{problem_slug}' not found")

            problem_data = response['data']['question']

            # Save to cache
            self._save_to_cache(problem_slug, problem_data)

            return problem_data

        except LeetCodeAPIError:
            raise
        except Exception as e:
            raise LeetCodeAPIError(
                f"Unexpected error fetching problem details: {e}")

    def _parse_example_testcases(self, example_testcases: str, meta_data: str) -> List[Tuple[Any, Any]]:
        """
        Parse example test cases from LeetCode format to Pyleet format.

        Args:
            example_testcases (str): Raw example test cases from LeetCode
            meta_data (str): Problem metadata containing parameter information

        Returns:
            List[Tuple[Any, Any]]: List of (input, expected_output) tuples
        """
        if not example_testcases:
            return []

        try:
            # Parse metadata to understand parameter structure
            meta = json.loads(meta_data) if meta_data else {}
            params = meta.get('params', [])

            # Split test cases by double newlines
            test_case_blocks = example_testcases.strip().split('\n\n')
            test_cases = []

            for block in test_case_blocks:
                lines = [line.strip()
                         for line in block.split('\n') if line.strip()]
                if len(lines) < len(params) + 1:  # Need inputs + expected output
                    continue

                # Parse inputs
                inputs = []
                # All but last line are inputs
                for line in lines[:-1]:
                    try:
                        # Try to parse as JSON first
                        parsed_input = json.loads(line)
                        inputs.append(parsed_input)
                    except json.JSONDecodeError:
                        # Fallback to string evaluation
                        try:
                            parsed_input = eval(line)
                            inputs.append(parsed_input)
                        except:
                            # Keep as string if all else fails
                            inputs.append(line)

                # Parse expected output (last line)
                expected_line = lines[-1]
                try:
                    expected_output = json.loads(expected_line)
                except json.JSONDecodeError:
                    try:
                        expected_output = eval(expected_line)
                    except:
                        expected_output = expected_line

                # Format inputs based on number of parameters
                if len(inputs) == 1:
                    formatted_input = inputs[0]
                else:
                    formatted_input = inputs

                test_cases.append((formatted_input, expected_output))

            return test_cases

        except Exception as e:
            # If parsing fails, return empty list rather than crashing
            print(f"Warning: Failed to parse example test cases: {e}")
            return []

    def get_test_cases(self, problem_slug: str) -> List[Tuple[Any, Any]]:
        """
        Get test cases for a LeetCode problem.

        Args:
            problem_slug (str): The problem slug (e.g., "two-sum")

        Returns:
            List[Tuple[Any, Any]]: List of (input, expected_output) tuples

        Raises:
            LeetCodeAPIError: If unable to fetch or parse test cases
        """
        problem_details = self.get_problem_details(problem_slug)

        example_testcases = problem_details.get('exampleTestcases', '')
        meta_data = problem_details.get('metaData', '')

        if not example_testcases:
            # Try alternative field
            example_testcases = problem_details.get('sampleTestCase', '')

        if not example_testcases:
            raise LeetCodeAPIError(
                f"No example test cases found for problem '{problem_slug}'")

        test_cases = self._parse_example_testcases(
            example_testcases, meta_data)

        if not test_cases:
            raise LeetCodeAPIError(
                f"Failed to parse test cases for problem '{problem_slug}'")

        return test_cases


def fetch_leetcode_testcases(problem_slug: str, cache_enabled: bool = True) -> List[Tuple[Any, Any]]:
    """
    Convenience function to fetch test cases from a LeetCode problem.

    Args:
        problem_slug (str): The problem slug (e.g., "two-sum")
        cache_enabled (bool): Whether to enable caching

    Returns:
        List[Tuple[Any, Any]]: List of (input, expected_output) tuples

    Raises:
        LeetCodeAPIError: If unable to fetch test cases
    """
    api = LeetCodeAPI(cache_enabled=cache_enabled)
    return api.get_test_cases(problem_slug)
